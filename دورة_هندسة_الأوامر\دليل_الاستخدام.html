<!doctype html>
<html lang="ar" dir="rtl" data-theme="light">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>دليل دورة هندسة الأوامر التفاعلية</title>
    <style>
      body {
        max-width: 880px;
        margin: 0 auto;
        padding: 32px 80px;
        position: relative;
        box-sizing: border-box;
        font-family: 'Times New Roman', serif;
        line-height: 1.6;
        color: #000;
        background: #fff;
        text-align: right;
      }
      h1 {
        text-align: center;
        font-size: 24px;
        margin-bottom: 40px;
        border-bottom: 2px solid #000;
        padding-bottom: 10px;
      }
      h2 {
        font-size: 20px;
        margin-top: 30px;
        margin-bottom: 15px;
        color: #2c3e50;
      }
      h3 {
        font-size: 18px;
        margin-top: 25px;
        margin-bottom: 12px;
        color: #34495e;
      }
      p {
        margin-bottom: 12px;
        text-align: justify;
      }
      ul,
      ol {
        margin-bottom: 15px;
        padding-right: 30px;
      }
      li {
        margin-bottom: 8px;
      }
      .highlight {
        background-color: #f8f9fa;
        border-right: 4px solid #3498db;
        padding: 15px;
        margin: 20px 0;
      }
      .note {
        background-color: #fff3cd;
        border: 1px solid #ffeeba;
        padding: 15px;
        margin: 20px 0;
        border-radius: 4px;
      }
      .warning {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        padding: 15px;
        margin: 20px 0;
        border-radius: 4px;
      }
      table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
      }
      th,
      td {
        border: 1px solid #ddd;
        padding: 12px;
        text-align: right;
      }
      th {
        background-color: #f8f9fa;
        font-weight: bold;
      }
      .footer {
        margin-top: 50px;
        text-align: center;
        font-style: italic;
        color: #666;
      }
    </style>
  </head>
  <body>
    <h1>دليل دورة هندسة الأوامر التفاعلية</h1>

    <div class="highlight">
      <p><strong>مرحباً بك في دورة هندسة الأوامر التفاعلية!</strong></p>
      <p>هذا الدليل يشرح لك كيفية استخدام الدورة والاستفادة القصوى من محتواها التفاعلي.</p>
    </div>

    <h2>📋 نظرة عامة على الدورة</h2>

    <h3>الهدف من الدورة</h3>
    <p>
      تهدف هذه الدورة إلى تعليم المتعلمين من المستوى المتوسط كيفية إتقان فن هندسة الأوامر (Prompt Engineering) للذكاء
      الاصطناعي. ستتعلم كيفية تحويل الأوامر العادية إلى أوامر قوية وفعالة تحقق نتائج مذهلة.
    </p>

    <h3>الجمهور المستهدف</h3>
    <ul>
      <li>المطورون والمبرمجون المهتمون بالذكاء الاصطناعي</li>
      <li>الباحثون في مجال الذكاء الاصطناعي</li>
      <li>المصممون التعليميون والتقنيون</li>
      <li>أي شخص يريد تحسين تفاعله مع نماذج الذكاء الاصطناعي</li>
    </ul>

    <h2>📚 محتويات الدورة</h2>

    <table>
      <tr>
        <th>الوحدة</th>
        <th>الموضوع</th>
        <th>المحتوى الرئيسي</th>
      </tr>
      <tr>
        <td>المقدمة</td>
        <td>تمهيد وتحفيز</td>
        <td>أهمية هندسة الأوامر وأثرها العملي</td>
      </tr>
      <tr>
        <td>الوحدة الأولى</td>
        <td>الأساسيات الصلبة</td>
        <td>مكونات البرومبت المثالي والأمثلة التطبيقية</td>
      </tr>
      <tr>
        <td>الوحدة الثانية</td>
        <td>المستوى المتقدم</td>
        <td>تقنيات CoT, ToT, RAG مع الأمثلة العملية</td>
      </tr>
      <tr>
        <td>الوحدة الثالثة</td>
        <td>الأدوات والتطبيقات</td>
        <td>التطبيقات العملية في مختلف المجالات</td>
      </tr>
      <tr>
        <td>الوحدة الرابعة</td>
        <td>الأمان والتعلم المستمر</td>
        <td>الحماية من الهجمات ونصائح التطوير</td>
      </tr>
      <tr>
        <td>الخاتمة</td>
        <td>التلخيص والتطبيق</td>
        <td>أهم النقاط وخطة العمل المستقبلية</td>
      </tr>
    </table>

    <h2>🖥️ كيفية استخدام الدورة</h2>

    <h3>متطلبات تقنية</h3>
    <ul>
      <li>متصفح ويب حديث (Chrome, Firefox, Safari, Edge)</li>
      <li>اتصال بالإنترنت (للوصول للروابط الخارجية)</li>
      <li>لا يتطلب أي تطبيقات إضافية</li>
    </ul>

    <h3>طريقة فتح الدورة</h3>
    <ol>
      <li>انقر نقراً مزدوجاً على ملف "دورة_هندسة_الأوامر_التفاعلية.html"</li>
      <li>سيفتح الملف في متصفحك الافتراضي</li>
      <li>يمكنك أيضاً سحب الملف إلى نافذة المتصفح</li>
    </ol>

    <h3>التنقل داخل الدورة</h3>
    <ul>
      <li>استخدم شريط التمرير للتنقل بين الأقسام</li>
      <li>انقر على العناوين في جدول المحتويات للانتقال المباشر</li>
      <li>استخدم أزرار "التالي" و "السابق" في نهاية كل قسم</li>
      <li>استخدم زر "العودة للأعلى" للرجوع لبداية الصفحة</li>
    </ul>

    <h2>📖 نصائح للتعلم الفعال</h2>

    <div class="note">
      <h3>💡 نصائح مهمة للاستفادة القصوى:</h3>
      <ul>
        <li><strong>اقرأ بتمعن:</strong> لا تتسرع في القراءة، خذ وقتك في فهم كل مفهوم</li>
        <li><strong>جرب الأمثلة:</strong> انسخ الأمثلة وجربها في نماذج الذكاء الاصطناعي</li>
        <li><strong>حل التحديات:</strong> حاول حل التحديات التطبيقية بنفسك قبل مراجعة الحلول</li>
        <li><strong>دون الملاحظات:</strong> احتفظ بدفتر ملاحظات للأفكار والأمثلة المهمة</li>
        <li><strong>طبق عملياً:</strong> استخدم ما تعلمته في مشاريعك الحقيقية</li>
      </ul>
    </div>

    <h3>العناصر التفاعلية في الدورة</h3>
    <ul>
      <li><strong>أمثلة قابلة للنسخ:</strong> صناديق الكود يمكن نسخها بنقرة واحدة</li>
      <li><strong>اختبارات الفهم:</strong> أسئلة تفاعلية في نهاية كل وحدة</li>
      <li><strong>التحديات التطبيقية:</strong> مهام عملية لتطبيق ما تعلمته</li>
      <li><strong>نصائح الخبراء:</strong> إرشادات مهمة من الممارسين المحترفين</li>
      <li><strong>قوائم التحقق:</strong> لضمان تطبيق أفضل الممارسات</li>
    </ul>

    <h2>🔧 استكشاف الأخطاء وحلها</h2>

    <h3>مشاكل شائعة وحلولها</h3>

    <div class="warning">
      <h4>⚠️ المشكلة: الدورة لا تفتح في المتصفح</h4>
      <p><strong>الحل:</strong> تأكد من أن ملف HTML سليم وغير تالف. جرب متصفح آخر.</p>
    </div>

    <div class="warning">
      <h4>⚠️ المشكلة: الخطوط لا تظهر بشكل صحيح</h4>
      <p><strong>الحل:</strong> تأكد من أن متصفحك يدعم الخطوط العربية. قد تحتاج لتحديث المتصفح.</p>
    </div>

    <div class="warning">
      <h4>⚠️ المشكلة: الروابط الخارجية لا تعمل</h4>
      <p><strong>الحل:</strong> تحقق من اتصالك بالإنترنت. بعض الروابط قد تكون مؤقتة.</p>
    </div>

    <h2>📈 خطة التعلم المقترحة</h2>

    <h3>الأسبوع الأول: الأساسيات</h3>
    <ul>
      <li>اقرأ المقدمة والوحدة الأولى</li>
      <li>جرب الأمثلة الأساسية</li>
      <li>حل اختبارات الفهم</li>
    </ul>

    <h3>الأسبوع الثاني: التقنيات المتقدمة</h3>
    <ul>
      <li>ادرس الوحدة الثانية بعمق</li>
      <li>طبق تقنيات CoT و ToT</li>
      <li>تعلم عن RAG وتطبيقاته</li>
    </ul>

    <h3>الأسبوع الثالث: التطبيق العملي</h3>
    <ul>
      <li>استكشف التطبيقات في مجالك</li>
      <li>حل التحديات التطبيقية</li>
      <li>بناء مجموعة أوامر شخصية</li>
    </ul>

    <h3>الأسبوع الرابع: الأمان والإتقان</h3>
    <ul>
      <li>تعلم تقنيات الأمان</li>
      <li>مراجعة شاملة للمحتوى</li>
      <li>وضع خطة للتعلم المستمر</li>
    </ul>

    <h2>📞 المساعدة والدعم</h2>

    <h3>للحصول على المساعدة</h3>
    <ul>
      <li>راجع قسم الأسئلة الشائعة في الدورة</li>
      <li>استخدم محركات البحث للاستفسارات التقنية</li>
      <li>انضم لمجتمعات الذكاء الاصطناعي العربية</li>
      <li>شارك في المنتديات المتخصصة</li>
    </ul>

    <h2>🌟 بعد إنهاء الدورة</h2>

    <h3>الخطوات التالية</h3>
    <ul>
      <li>طبق ما تعلمته في مشاريعك الحقيقية</li>
      <li>ابني مكتبة شخصية من الأوامر الفعالة</li>
      <li>تابع أحدث التطورات في مجال الذكاء الاصطناعي</li>
      <li>شارك معرفتك مع الآخرين</li>
      <li>استمر في التعلم والتطوير</li>
    </ul>

    <div class="highlight">
      <h3>🎯 تذكر دائماً:</h3>
      <p>
        هندسة الأوامر مهارة تتطور بالممارسة. كلما طبقت أكثر، كلما أصبحت أكثر إتقاناً. لا تتردد في التجريب والاستكشاف!
      </p>
    </div>

    <div class="footer">
      <p>نتمنى لك رحلة تعلم ممتعة ومثمرة في عالم هندسة الأوامر! 🚀</p>
      <p><em>تم إنشاء هذا الدليل بواسطة الذكاء الاصطناعي - 2024</em></p>
    </div>
  </body>
</html>

    <script id="html_badge_script1">
        window.__genspark_remove_badge_link = "https://www.genspark.ai/api/html_badge/" +
            "remove_badge?token=To%2FBnjzloZ3UfQdcSaYfDgkFHHWMSZ65vJjFuGzrMr%2BiiTcc8BRsJ%2FCe4qe%2BKB3jbhVEuji7W9uUvM2FNlpbyZ%2FnxeWF%2BK7XIVXNYMWn%2F%2FRInC98aGXjQeqypIUthx24DFO17kA0agD2P0uh%2BUwC5C04Og0HZ4EPGtI%2F4QrEQ3FTokG6Db2ZRPk9JiXu%2Bu6RBW5KQjUtqyy85T40wIYq%2FcZU47fzI8HV%2BxoxHZD4DiXIKRm7bC1nvkN1JPc%2F7Sd8avA8Uy%2BZF%2BC3Z02jv%2F3Ir6zQEPzdJP9T5PMa2HJWWVrnJ8qWwIMEQmZC%2Bsgo2Az2V0W%2BJ1RTqfotzMEFPWzNQLbDitT%2F8a%2BfSh2gLBj8Lq43nhF3cd3MP0F7hx%2Bvem2VgzU%2BE5jhXKsPrUB3pt%2BNoTMmfWd9mL7LiLID4cskHuY3tumxP9RWqixxvkYKo2DDJhw8m3k6TAnvOOoXTi7XYnPbCckX%2B8YSsmHxUK5ogLsukranB8mcXOTsdCsDCehDMwhwOlu1Znz5qStpcA0Lo%2BAytz1uUkALDMun%2B8vsA4XU54oioJiAPczsDKoN6laq";
        window.__genspark_locale = "en-US";
        window.__genspark_token = "To/BnjzloZ3UfQdcSaYfDgkFHHWMSZ65vJjFuGzrMr+iiTcc8BRsJ/Ce4qe+KB3jbhVEuji7W9uUvM2FNlpbyZ/nxeWF+K7XIVXNYMWn//RInC98aGXjQeqypIUthx24DFO17kA0agD2P0uh+UwC5C04Og0HZ4EPGtI/4QrEQ3FTokG6Db2ZRPk9JiXu+u6RBW5KQjUtqyy85T40wIYq/cZU47fzI8HV+xoxHZD4DiXIKRm7bC1nvkN1JPc/7Sd8avA8Uy+ZF+C3Z02jv/3Ir6zQEPzdJP9T5PMa2HJWWVrnJ8qWwIMEQmZC+sgo2Az2V0W+J1RTqfotzMEFPWzNQLbDitT/8a+fSh2gLBj8Lq43nhF3cd3MP0F7hx+vem2VgzU+E5jhXKsPrUB3pt+NoTMmfWd9mL7LiLID4cskHuY3tumxP9RWqixxvkYKo2DDJhw8m3k6TAnvOOoXTi7XYnPbCckX+8YSsmHxUK5ogLsukranB8mcXOTsdCsDCehDMwhwOlu1Znz5qStpcA0Lo+Aytz1uUkALDMun+8vsA4XU54oioJiAPczsDKoN6laq";
    </script>
    
        <script id="html_badge_script2" src="https://www.genspark.ai/html_badge.js"></script>
        
    <script id="html_notice_dialog_script" src="https://www.genspark.ai/notice_dialog.js"></script>
    