@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Primary Colors */
  --color-primary: #2563EB; /* blue-600 */
  --color-primary-50: #EFF6FF; /* blue-50 */
  --color-primary-100: #DBEAFE; /* blue-100 */
  --color-primary-500: #3B82F6; /* blue-500 */
  --color-primary-600: #2563EB; /* blue-600 */
  --color-primary-700: #1D4ED8; /* blue-700 */
  
  /* Secondary Colors */
  --color-secondary: #059669; /* emerald-600 */
  --color-secondary-50: #ECFDF5; /* emerald-50 */
  --color-secondary-100: #D1FAE5; /* emerald-100 */
  --color-secondary-500: #10B981; /* emerald-500 */
  --color-secondary-600: #059669; /* emerald-600 */
  --color-secondary-700: #047857; /* emerald-700 */
  
  /* Accent Colors */
  --color-accent: #DC2626; /* red-600 */
  --color-accent-50: #FEF2F2; /* red-50 */
  --color-accent-100: #FEE2E2; /* red-100 */
  --color-accent-500: #EF4444; /* red-500 */
  --color-accent-600: #DC2626; /* red-600 */
  --color-accent-700: #B91C1C; /* red-700 */
  
  /* Background Colors */
  --color-background: #FAFAFA; /* gray-50 */
  --color-surface: #FFFFFF; /* white */
  
  /* Text Colors */
  --color-text-primary: #1F2937; /* gray-800 */
  --color-text-secondary: #6B7280; /* gray-500 */
  
  /* Status Colors */
  --color-success: #10B981; /* emerald-500 */
  --color-success-50: #ECFDF5; /* emerald-50 */
  --color-success-100: #D1FAE5; /* emerald-100 */
  
  --color-warning: #F59E0B; /* amber-500 */
  --color-warning-50: #FFFBEB; /* amber-50 */
  --color-warning-100: #FEF3C7; /* amber-100 */
  
  --color-error: #EF4444; /* red-500 */
  --color-error-50: #FEF2F2; /* red-50 */
  --color-error-100: #FEE2E2; /* red-100 */
  
  /* Border Colors */
  --color-border: #E5E7EB; /* gray-200 */
  --color-border-light: #F3F4F6; /* gray-100 */
  
  /* Shadow Variables */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* Animation Variables */
  --transition-fast: 200ms ease-out;
  --transition-normal: 300ms ease-out;
  --spring-bounce: cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Base Styles */
@layer base {
  html {
    direction: rtl;
  }
  
  body {
    font-family: 'Noto Sans Arabic', sans-serif;
    background-color: var(--color-background);
    color: var(--color-text-primary);
    line-height: 1.6;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Tajawal', sans-serif;
    font-weight: 500;
    line-height: 1.3;
  }
  
  code, pre {
    font-family: 'JetBrains Mono', monospace;
    direction: ltr;
  }
  
  .caption {
    font-family: 'Cairo', sans-serif;
    font-weight: 300;
  }
}

/* Component Styles */
@layer components {
.card  {
    @apply bg-white rounded-lg border border-gray-200;
    transition: all var(--transition-fast);
  }
  
  .card:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-md);
  }
  
  .btn-primary {
    @apply bg-primary text-white px-6 py-3 rounded-lg font-medium;
    transition: all var(--transition-fast);
    min-height: 44px;
    min-width: 44px;
  }
  
  .btn-primary:hover {
    @apply bg-primary-700;
    transform: translateY(-1px);
  }
  
  .btn-secondary {
    @apply bg-secondary text-white px-6 py-3 rounded-lg font-medium;
    transition: all var(--transition-fast);
    min-height: 44px;
    min-width: 44px;
  }
  
  .btn-secondary:hover {
    @apply bg-secondary-700;
    transform: translateY(-1px);
  }
  
  .floating-action {
    @apply fixed z-50 backdrop-blur-sm bg-white/90 shadow-lg rounded-full;
    transition: all var(--transition-fast);
  }
.progress-indicator  {
    @apply sticky top-4 z-40 backdrop-blur-sm bg-white/95 rounded-lg border border-gray-200;
  }
  
  .syntax-highlight {
    @apply bg-gradient-to-r from-primary-50 to-secondary-50 border-l-4 border-primary-500 p-4 rounded-r-lg;
  }
  
  .collapsible-content {
    transition: height var(--transition-normal) ease-out;
    overflow: hidden;
  }
  
  .chevron-icon {
    transition: transform var(--transition-fast);
  }
  
  .chevron-icon.expanded {
    transform: rotate(180deg);
  }
  
  .skeleton {
    @apply animate-pulse bg-gray-200 rounded;
  }
  
  .success-animation {
    animation: success-bounce 0.6s var(--spring-bounce);
  }
  
  @keyframes success-bounce {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
  }
  
  .micro-interaction {
    transition: transform var(--transition-fast);
  }
  
  .micro-interaction:hover {
    transform: scale(1.02);
  }
  
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Utility Classes */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  
  .rtl {
    direction: rtl;
  }
  
  .ltr {
    direction: ltr;
  }
  
  .shadow-custom {
    box-shadow: var(--shadow-base);
  }
  
  .shadow-custom-lg {
    box-shadow: var(--shadow-lg);
  }
  
  .transition-custom {
    transition: all var(--transition-fast);
  }
  
  .transition-custom-slow {
    transition: all var(--transition-normal);
  }
}